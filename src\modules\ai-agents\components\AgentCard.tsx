import {
  IconCard,
  Modal,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/format';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent với thiết kế futuristic
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);

  // Format ngày tạo
  const createdDate = formatDate(agent.createdAt, 'DD/MM/YYYY');

  return (
    <>
      <div
        className="relative group h-full"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Holographic background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-cyan-500/10 rounded-xl blur-sm group-hover:blur-none transition-all duration-500" />

        {/* Animated border */}
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-cyan-500/20 p-[1px] group-hover:from-purple-500/40 group-hover:via-blue-500/40 group-hover:to-cyan-500/40 transition-all duration-500">
          <div className="h-full w-full rounded-xl bg-gray-900/95 dark:bg-gray-800/95 backdrop-blur-sm">
            {/* Glow effect */}
            <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-purple-500/5 via-blue-500/5 to-cyan-500/5 group-hover:from-purple-500/10 group-hover:via-blue-500/10 group-hover:to-cyan-500/10 transition-all duration-500" />

            <div className="relative p-6 h-full">
              {/* Header với avatar và thông tin cơ bản */}
              <div className="flex items-start gap-4 mb-6">
                {/* Avatar với level bao quanh */}
                <div className="relative flex-shrink-0">
                  {/* Level ring bao quanh avatar */}
                  <div className="relative w-20 h-20">
                    {/* Outer glow ring */}
                    <div className={`absolute inset-0 rounded-full bg-gradient-to-r transition-all duration-500 ${
                      isActive
                        ? 'from-green-400/50 via-emerald-400/50 to-teal-400/50 shadow-lg shadow-green-400/25'
                        : 'from-gray-400/30 via-gray-500/30 to-gray-600/30'
                    } p-[2px] group-hover:p-[3px]`}>
                      <div className="w-full h-full rounded-full bg-gray-900/80 dark:bg-gray-800/80 backdrop-blur-sm" />
                    </div>

                    {/* Level text bao quanh */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="relative w-16 h-16 rounded-full border-2 border-gradient-to-r from-purple-400 to-cyan-400 flex items-center justify-center">
                        {/* Avatar */}
                        <img
                          src={agent.avatar || '/assets/images/default-avatar.png'}
                          alt={agent.name}
                          className="w-14 h-14 rounded-full object-cover"
                        />

                        {/* Level badge */}
                        <div className="absolute -top-1 -right-1 bg-gradient-to-r from-purple-500 to-cyan-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                          {agent.level}
                        </div>
                      </div>
                    </div>

                    {/* Badge frame overlay */}
                    {agent.badgeUrl && (
                      <img
                        src={agent.badgeUrl}
                        alt="Level frame"
                        className="absolute inset-0 w-full h-full object-contain z-10 drop-shadow-lg opacity-80"
                      />
                    )}

                    {/* Status indicator với pulse effect */}
                    <div className="absolute -bottom-1 -right-1 z-20">
                      <div className={`w-4 h-4 rounded-full ${
                        isActive ? 'bg-green-400' : 'bg-gray-400'
                      } shadow-lg border-2 border-gray-900`}>
                        {isActive && (
                          <div className="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-75" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Agent info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-3">
                    <div className="min-w-0 flex-1">
                      <h3 className="text-lg font-bold text-white truncate mb-1 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300">
                        {agent.name}
                      </h3>
                      <p className="text-sm text-gray-400 mb-1">{agent.typeName}</p>
                      <p className="text-xs text-gray-500">{createdDate}</p>
                    </div>

                    {/* Model chip với glow effect */}
                    <div className="flex-shrink-0">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-sm group-hover:blur-none transition-all duration-300" />
                        <div className="relative px-3 py-1 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full">
                          <span className="text-xs font-medium text-blue-300">{agent.modelId}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Level và Experience với animated progress */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-bold text-white">Level {agent.level}</span>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs text-gray-400">{currentExp}/{maxExp} EXP</span>
                  </div>

                  {/* Experience percentage với glow */}
                  <div className="relative">
                    <div className={`absolute inset-0 rounded-full blur-sm transition-all duration-300 ${
                      experiencePercent >= 80 ? 'bg-green-400/30' :
                      experiencePercent >= 50 ? 'bg-yellow-400/30' : 'bg-red-400/30'
                    }`} />
                    <div className={`relative px-2 py-1 rounded-full text-xs font-bold transition-all duration-300 ${
                      experiencePercent >= 80 ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                      experiencePercent >= 50 ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                      'bg-red-500/20 text-red-300 border border-red-500/30'
                    }`}>
                      {experiencePercent}%
                    </div>
                  </div>
                </div>

                {/* Animated progress bar */}
                <div className="relative h-2 bg-gray-700/50 rounded-full overflow-hidden">
                  {/* Background glow */}
                  <div className={`absolute inset-0 rounded-full transition-all duration-500 ${
                    experiencePercent >= 80 ? 'bg-green-400/10' :
                    experiencePercent >= 50 ? 'bg-yellow-400/10' : 'bg-red-400/10'
                  }`} />

                  {/* Progress fill với gradient và animation */}
                  <div
                    className={`absolute left-0 top-0 h-full rounded-full transition-all duration-1000 ease-out ${
                      experiencePercent >= 80 ? 'bg-gradient-to-r from-green-400 to-emerald-400' :
                      experiencePercent >= 50 ? 'bg-gradient-to-r from-yellow-400 to-orange-400' :
                      'bg-gradient-to-r from-red-400 to-pink-400'
                    }`}
                    style={{ width: `${experiencePercent}%` }}
                  >
                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
                  </div>

                  {/* Particle effects */}
                  {isHovered && (
                    <div className="absolute inset-0">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className={`absolute w-1 h-1 rounded-full animate-ping ${
                            experiencePercent >= 80 ? 'bg-green-400' :
                            experiencePercent >= 50 ? 'bg-yellow-400' : 'bg-red-400'
                          }`}
                          style={{
                            left: `${Math.random() * experiencePercent}%`,
                            animationDelay: `${i * 0.2}s`
                          }}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Action buttons với IconCard */}
              <div className="flex justify-center gap-4">
                <IconCard
                  icon="power"
                  variant={isActive ? 'primary' : 'default'}
                  size="md"
                  onClick={handleToggleActive}
                  className={`transition-all duration-300 ${
                    isActive
                      ? 'text-green-400 bg-green-500/20 border-green-500/30 hover:bg-green-500/30 shadow-lg shadow-green-400/25'
                      : 'text-gray-400 bg-gray-500/20 border-gray-500/30 hover:bg-gray-500/30'
                  }`}
                  disabled={toggleAgentActiveMutation.isPending}
                  title={isActive ? t('common.deactivate') : t('common.activate')}
                  tooltipPosition="top"
                />

                <IconCard
                  icon="clock"
                  variant="default"
                  size="md"
                  onClick={handleMemoriesClick}
                  className="text-blue-400 bg-blue-500/20 border-blue-500/30 hover:bg-blue-500/30 shadow-lg shadow-blue-400/25 transition-all duration-300"
                  title={t('aiAgents:memories.title', 'Memories')}
                  tooltipPosition="top"
                />

                <IconCard
                  icon="edit"
                  variant="default"
                  size="md"
                  onClick={handleEditAgent}
                  className="text-purple-400 bg-purple-500/20 border-purple-500/30 hover:bg-purple-500/30 shadow-lg shadow-purple-400/25 transition-all duration-300"
                  title={t('common.edit')}
                  tooltipPosition="top"
                />

                <IconCard
                  icon="trash"
                  variant="default"
                  size="md"
                  onClick={handleDeleteClick}
                  className="text-red-400 bg-red-500/20 border-red-500/30 hover:bg-red-500/30 shadow-lg shadow-red-400/25 transition-all duration-300"
                  disabled={deleteAgentMutation.isPending}
                  title={t('common.delete')}
                  tooltipPosition="top"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Futuristic Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">
            <IconCard
              icon="x"
              title={t('common.cancel', 'Hủy')}
              onClick={handleDeleteCancel}
              variant="default"
              className="bg-gray-500/20 border-gray-500/30 hover:bg-gray-500/30"
            />
            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
              className="bg-red-500/20 border-red-500/30 hover:bg-red-500/30"
            />
          </div>
        }
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            {t('deleteConfirmMessage', 'Bạn có chắc chắn muốn xóa Agent này không? Hành động này không thể hoàn tác.')}
          </p>

          <div className="relative p-4 rounded-xl bg-gray-800/50 border border-gray-700/50">
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-red-500/5 to-pink-500/5" />
            <div className="relative flex items-center gap-3">
              <div className="relative">
                <img
                  src={agent.avatar || '/assets/images/default-avatar.png'}
                  alt={agent.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div className="absolute inset-0 rounded-full bg-red-500/20 animate-pulse" />
              </div>
              <div>
                <p className="font-medium text-white">{agent.name}</p>
                <p className="text-sm text-gray-400">{agent.typeName}</p>
                <p className="text-xs text-gray-500">Level {agent.level}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
